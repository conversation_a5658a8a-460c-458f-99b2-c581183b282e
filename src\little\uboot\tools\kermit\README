# SPDX-License-Identifier: GPL-2.0+
#
# (C) Copyright 2001
# <PERSON>, DENX Software Engineering, <EMAIL>.

This directory contains scripts that help to perform certain actions
that need to be done frequently when working with U-Boot.

They are meant as EXAMPLE code, so it is very likely that you will
have to modify them before use.


Short description:
==================

dot.kermrc:

	Example for "~/.kermrc" Kermit init file for use with U-Boot

	by <PERSON>, 24 Jun 2001

flash_param:

	"kermit" script to automatically initialize the environment
	variables on your target. This is most useful during
	development when your environment variables are stored in an
	embedded flash sector which is erased whenever you install a
	new U-Boot image.

	by <PERSON><PERSON>, 10 May 2001

send_cmd:

	send_cmd U_BOOT_COMMAND

	"kermit" script to send a U-Boot command and print the
	results. When used from a shell with history (like the bash)
	this indirectly adds kind of history to U-Boot ;-)

	by <PERSON><PERSON>, 10 May 2001

send_image:

	send_image FILE_NAME OFFSET

	"kermit" script to automatically download a file to the
	target using the "loadb" command (kermit binary protocol)

	by <PERSON><PERSON>, 10 May 2001
