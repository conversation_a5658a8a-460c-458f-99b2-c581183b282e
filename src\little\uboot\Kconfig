#
# For a description of the syntax of this configuration file,
# see the file Documentation/kbuild/kconfig-language.txt in the
# Linux kernel source tree.
#
mainmenu "U-Boot $(UBOOTVERSION) Configuration"

comment "Compiler: $(CC_VERSION_TEXT)"

source "scripts/Kconfig.include"

# Allow defaults in arch-specific code to override any given here
source "arch/Kconfig"

menu "General setup"

config BROKEN
	bool
	help
	  This option cannot be enabled. It is used as dependency
	  for broken and incomplete features.

config DEPRECATED
	bool
	help
	  This option cannot be enabled.  It it used as a dependency for
	  code that relies on deprecated features that will be removed and
	  the conversion deadline has passed.

config LOCALVERSION
	string "Local version - append to U-Boot release"
	help
	  Append an extra string to the end of your U-Boot version.
	  This will show up in your boot log, for example.
	  The string you set here will be appended after the contents of
	  any files with a filename matching localversion* in your
	  object and source tree, in that order.  Your total string can
	  be a maximum of 64 characters.

config LOCALVERSION_AUTO
	bool "Automatically append version information to the version string"
	default y
	help
	  This will try to automatically determine if the current tree is a
	  release tree by looking for Git tags that belong to the current
	  top of tree revision.

	  A string of the format -gxxxxxxxx will be added to the localversion
	  if a Git-based tree is found.  The string generated by this will be
	  appended after any matching localversion* files, and after the value
	  set in CONFIG_LOCALVERSION.

	  (The actual string used here is the first eight characters produced
	  by running the command:

	    $ git rev-parse --verify HEAD

	  which is done within the script "scripts/setlocalversion".)

config CC_IS_GCC
	def_bool $(success,$(CC) --version | head -n 1 | grep -q gcc)

config GCC_VERSION
	int
	default $(shell,$(srctree)/scripts/gcc-version.sh -p $(CC) | sed 's/^0*//') if CC_IS_GCC
	default 0

config CC_IS_CLANG
	def_bool $(success,$(CC) --version | head -n 1 | grep -q clang)

config CLANG_VERSION
	int
	default $(shell,$(srctree)/scripts/clang-version.sh $(CC))

choice
	prompt "Optimization level"
	default CC_OPTIMIZE_FOR_SIZE

config CC_OPTIMIZE_FOR_SIZE
	bool "Optimize for size"
	help
	  Enabling this option will pass "-Os" to gcc, resulting in a smaller
	  U-Boot image.

	  This option is enabled by default for U-Boot.

config CC_OPTIMIZE_FOR_SPEED
	bool "Optimize for speed"
	help
	  Enabling this option will pass "-O2" to gcc, resulting in a faster
	  U-Boot image.

config CC_OPTIMIZE_FOR_DEBUG
	bool "Optimize for debugging"
	help
	  Enabling this option will pass "-Og" to gcc, enabling optimizations
	  which don't interfere with debugging.

endchoice

config OPTIMIZE_INLINING
	bool "Allow compiler to uninline functions marked 'inline' in full U-Boot"
	help
	  This option determines if U-Boot forces gcc to inline the functions
	  developers have marked 'inline'. Doing so takes away freedom from gcc to
	  do what it thinks is best, which is desirable in some cases for size
	  reasons.

config SPL_OPTIMIZE_INLINING
	bool "Allow compiler to uninline functions marked 'inline' in SPL"
	depends on SPL
	help
	  This option determines if U-Boot forces gcc to inline the functions
	  developers have marked 'inline'. Doing so takes away freedom from gcc to
	  do what it thinks is best, which is desirable in some cases for size
	  reasons.

config ARCH_SUPPORTS_LTO
	bool

config LTO
	bool "Enable Link Time Optimizations"
	depends on ARCH_SUPPORTS_LTO
	help
	  This option enables Link Time Optimization (LTO), a mechanism which
	  allows the compiler to optimize between different compilation units.

	  This can optimize away dead code paths, resulting in smaller binary
	  size (if CC_OPTIMIZE_FOR_SIZE is enabled).

	  This option is not available for every architecture and may
	  introduce bugs.

	  Currently, when compiling with GCC, due to a weird bug regarding
	  jobserver, the final linking will not respect make's --jobs argument.
	  Instead all available processors will be used (as reported by the
	  nproc command).

	  If unsure, say n.

config TPL_OPTIMIZE_INLINING
	bool "Allow compiler to uninline functions marked 'inline' in TPL"
	depends on TPL
	help
	  This option determines if U-Boot forces gcc to inline the functions
	  developers have marked 'inline'. Doing so takes away freedom from gcc to
	  do what it thinks is best, which is desirable in some cases for size
	  reasons.

config CC_COVERAGE
	bool "Enable code coverage analysis"
	depends on SANDBOX
	help
	  Enabling this option will pass "--coverage" to gcc to compile
	  and link code instrumented for coverage analysis.

config ASAN
	bool "Enable AddressSanitizer"
	depends on SANDBOX
	help
	  Enables AddressSanitizer to discover out-of-bounds accesses,
	  use-after-free, double-free and memory leaks.

config FUZZ
	bool "Enable fuzzing"
	depends on CC_IS_CLANG
	depends on DM_FUZZING_ENGINE
	select ASAN
	help
	  Enables the fuzzing infrastructure to generate fuzzing data and run
          fuzz tests.

config CC_HAS_ASM_INLINE
	def_bool $(success,echo 'void foo(void) { asm inline (""); }' | $(CC) -x c - -c -o /dev/null)

config XEN
	bool "Select U-Boot be run as a bootloader for XEN Virtual Machine"
	help
	  Enabling this option will make U-Boot be run as a bootloader
	  for XEN [1] Virtual Machine.

	  Xen is a virtual machine monitor (VMM) or a type-1 hypervisor with support
	  for para-virtualization. Xen can organize the safe execution of several
	  virtual machines on the same physical system with performance close to
	  native. It is used as the basis for a number of different commercial and
	  open source applications, such as: server virtualization, Infrastructure
	  as a Service (IaaS), desktop virtualization, security applications,
	  embedded and hardware appliances.
	  Xen has a special VM called Domain-0 that runs the Dom0 kernel and allows
	  Xen to use the device drivers for the Domain-0 kernel by default.

	  [1] - https://xenproject.org/

config DISTRO_DEFAULTS
	bool "Select defaults suitable for booting general purpose Linux distributions"
	select AUTO_COMPLETE
	select CMDLINE_EDITING
	select CMD_BOOTI if ARM64
	select CMD_BOOTZ if ARM && !ARM64
	select CMD_DHCP if CMD_NET
	select CMD_ENV_EXISTS
	select CMD_EXT2
	select CMD_EXT4
	select CMD_FAT
	select CMD_FS_GENERIC
	select CMD_PART if PARTITIONS
	select CMD_PING if CMD_NET
	select CMD_PXE if NET
	select CMD_SYSBOOT
	select ENV_VARS_UBOOT_CONFIG
	select HUSH_PARSER
	select SUPPORT_RAW_INITRD
	select SYS_LONGHELP
	imply CMD_MII if NET
	imply USB_STORAGE
	imply USE_BOOTCOMMAND
	help
	  Select this to enable various options and commands which are suitable
	  for building u-boot for booting general purpose Linux distributions.

config ENV_VARS_UBOOT_CONFIG
	bool "Add arch, board, vendor and soc variables to default environment"
	help
	  Define this in order to add variables describing the
	  U-Boot build configuration to the default environment.
	  These will be named arch, cpu, board, vendor, and soc.
	  Enabling this option will cause the following to be defined:
	  - CONFIG_SYS_ARCH
	  - CONFIG_SYS_CPU
	  - CONFIG_SYS_BOARD
	  - CONFIG_SYS_VENDOR
	  - CONFIG_SYS_SOC

config NR_DRAM_BANKS
	int "Number of DRAM banks"
	default 1 if ARCH_SUNXI || ARCH_OWL
	default 4
	help
	  This defines the number of DRAM banks.

config SYS_BOOT_GET_CMDLINE
	bool "Enable kernel command line setup"
	help
	  Enables allocating and saving kernel cmdline in space between
	  "bootm_low" and "bootm_low" + BOOTMAPSZ.

config SYS_BARGSIZE
	int "Size of kernel command line buffer in bytes"
	depends on SYS_BOOT_GET_CMDLINE
	default 512
	help
	  Buffer size for Boot Arguments which are passed to the application
	  (usually a Linux kernel) when it is booted

config SYS_BOOT_GET_KBD
	bool "Enable kernel board information setup"
	help
	  Enables allocating and saving a kernel copy of the bd_info in
	  space between "bootm_low" and "bootm_low" + BOOTMAPSZ.

config HAS_CUSTOM_SYS_INIT_SP_ADDR
	bool "Use a custom location for the initial stack pointer address"
	depends on ARC || (ARM && !INIT_SP_RELATIVE) || MIPS || PPC || RISCV
	default y if TFABOOT
	help
	  Typically, we use an initial stack pointer address that is calculated
	  by taking the statically defined CONFIG_SYS_INIT_RAM_ADDR, adding the
	  statically defined CONFIG_SYS_INIT_RAM_SIZE and then subtracting the
	  build-time constant of GENERATED_GBL_DATA_SIZE.  On MIPS a different
	  but statica calculation is performed.  However, some platforms will
	  take a different approach.  Say Y here to define the address statically
	  instead.

config CUSTOM_SYS_INIT_SP_ADDR
	hex "Static location for the initial stack pointer"
	depends on HAS_CUSTOM_SYS_INIT_SP_ADDR
	default SYS_TEXT_BASE if TFABOOT

config SYS_MALLOC_F
	bool "Enable malloc() pool before relocation"
	default y if DM

	help
	  Before relocation, memory is very limited on many platforms. Still,
	  we can provide a small malloc() pool if needed. Driver model in
	  particular needs this to operate, so that it can allocate the
	  initial serial device and any others that are needed.

config SYS_MALLOC_F_LEN
	hex "Size of malloc() pool before relocation"
	depends on SYS_MALLOC_F
	default 0x400 if M68K || PPC || ROCKCHIP_PX30 || ROCKCHIP_RK3036 || \
			 ROCKCHIP_RK3308 || ROCKCHIP_RV1108
	default 0x600 if ARCH_ZYNQMP_R5 || ARCH_ZYNQMP
	default 0x800 if ARCH_ZYNQ || ROCKCHIP_RK3128 || ROCKCHIP_RK3188 || \
			 ROCKCHIP_RK322X || X86
	default 0x1000 if ARCH_MESON || ARCH_BMIPS || ARCH_MTMIPS
	default 0x1800 if ARCH_TEGRA
	default 0x4000 if SANDBOX || RISCV || ARCH_APPLE || ROCKCHIP_RK3368 || \
			  ROCKCHIP_RK3399
	default 0x8000 if RCAR_GEN3
	default 0x10000 if ARCH_IMX8 || ARCH_IMX8M
	default 0x2000
	help
	  Before relocation, memory is very limited on many platforms. Still,
	  we can provide a small malloc() pool if needed. Driver model in
	  particular needs this to operate, so that it can allocate the
	  initial serial device and any others that are needed.

config SYS_MALLOC_LEN
	hex "Define memory for Dynamic allocation"
	default 0x4000000 if SANDBOX
	default 0x2000000 if ARCH_ROCKCHIP || ARCH_OMAP2PLUS || ARCH_MESON
	default 0x200000 if ARCH_BMIPS || X86
	default 0x120000 if MACH_SUNIV
	default 0x220000 if MACH_SUN8I_V3S
	default 0x4020000 if ARCH_SUNXI
	default 0x400000
	help
	  This defines memory to be allocated for Dynamic allocation
	  TODO: Use for other architectures

config SPL_SYS_MALLOC_F_LEN
	hex "Size of malloc() pool in SPL"
	depends on SYS_MALLOC_F && SPL
	default 0 if !SPL_FRAMEWORK
	default 0x2800 if RCAR_GEN3
	default 0x2000 if IMX8MQ
	default SYS_MALLOC_F_LEN
	help
	  In SPL memory is very limited on many platforms. Still,
	  we can provide a small malloc() pool if needed. Driver model in
	  particular needs this to operate, so that it can allocate the
	  initial serial device and any others that are needed.

	  It is possible to enable CONFIG_SYS_SPL_MALLOC_START to start a new
	  malloc() region in SDRAM once it is inited.

config TPL_SYS_MALLOC_F_LEN
	hex "Size of malloc() pool in TPL"
	depends on SYS_MALLOC_F && TPL
	default SPL_SYS_MALLOC_F_LEN
	help
	  In TPL memory is very limited on many platforms. Still,
	  we can provide a small malloc() pool if needed. Driver model in
	  particular needs this to operate, so that it can allocate the
	  initial serial device and any others that are needed.

config VALGRIND
	bool "Inform valgrind about memory allocations"
	depends on !RISCV
	help
	  Valgrind is an instrumentation framework for building dynamic analysis
	  tools. In particular, it may be used to detect memory management bugs
	  in U-Boot. It relies on knowing when heap blocks are allocated in
	  order to give accurate results. This happens automatically for
	  standard allocator functions provided by the host OS. However, this
	  doesn't automatically happen for U-Boot's malloc implementation.

	  Enable this option to annotate U-Boot's malloc implementation so that
	  it can be handled accurately by Valgrind. If you aren't planning on
	  using valgrind to debug U-Boot, say 'n'.

config VPL_SYS_MALLOC_F_LEN
	hex "Size of malloc() pool in VPL before relocation"
	depends on SYS_MALLOC_F && VPL
	default SYS_MALLOC_F_LEN
	help
	  Before relocation, memory is very limited on many platforms. Still,
	  we can provide a small malloc() pool if needed. Driver model in
	  particular needs this to operate, so that it can allocate the
	  initial serial device and any others that are needed.

menuconfig EXPERT
	bool "Configure standard U-Boot features (expert users)"
	default y
	help
	  This option allows certain base U-Boot options and settings
	  to be disabled or tweaked. This is for specialized
	  environments which can tolerate a "non-standard" U-Boot.
	  Use this only if you really know what you are doing.

if EXPERT
	config SYS_MALLOC_CLEAR_ON_INIT
	bool "Init with zeros the memory reserved for malloc (slow)"
	default y
	help
	  This setting is enabled by default. The reserved malloc
	  memory is initialized with zeros, so first malloc calls
	  will return the pointer to the zeroed memory. But this
	  slows the boot time.

	  It is recommended to disable it, when CONFIG_SYS_MALLOC_LEN
	  value, has more than few MiB, e.g. when uses bzip2 or bmp logo.
	  Then the boot time can be significantly reduced.
	  Warning:
	  When disabling this, please check if malloc calls, maybe
	  should be replaced by calloc - if one expects zeroed memory.

config SYS_MALLOC_DEFAULT_TO_INIT
	bool "Default malloc to init while reserving the memory for it"
	help
	  It may happen that one needs to move the dynamic allocation
	  from one to another memory range, eg. when moving the malloc
	  from the limited static to a potentially large dynamic (DDR)
	  memory.

	  If so then on top of setting the updated memory aside one
	  needs to bring the malloc init.

	  If such a scenario is sought choose yes.

config TOOLS_DEBUG
	bool "Enable debug information for tools"
	help
	  Enable generation of debug information for tools such as mkimage.
	  This can be used for debugging purposes. With debug information
	  it is possible to set breakpoints on particular lines, single-step
	  debug through the source code, etc.

endif # EXPERT

config PHYS_64BIT
	bool "64bit physical address support"
	help
	  Say Y here to support 64bit physical memory address.
	  This can be used not only for 64bit SoCs, but also for
	  large physical address extension on 32bit SoCs.

config HAS_ROM
	bool
	select BINMAN
	help
	  Enables building of a u-boot.rom target. This collects U-Boot and
	  any necessary binary blobs.

config SPL_IMAGE
	string "SPL image used in the combined SPL+U-Boot image"
	default "spl/boot.bin" if ARCH_AT91 && SPL_NAND_SUPPORT
	default "spl/u-boot-spl.bin"
	depends on SPL
	help
	  Select the SPL build target that shall be generated by the SPL
	  build process (default spl/u-boot-spl.bin). This image will be
	  used to generate a combined image with SPL and main U-Boot
	  proper as one single image.

config REMAKE_ELF
	bool "Recreate an ELF image from raw U-Boot binary"
	help
	  Enable this to recreate an ELF image (u-boot.elf) from the raw
	  U-Boot binary (u-boot.bin), which may already have been statically
	  relocated and may already have a device-tree appended to it.

config BUILD_TARGET
	string "Build target special images"
	default "u-boot-with-spl.sfp" if TARGET_SOCFPGA_ARRIA10
	default "u-boot-with-spl.sfp" if TARGET_SOCFPGA_GEN5
	default "u-boot-spl.kwb" if ARCH_MVEBU && SPL
	default "u-boot-elf.srec" if RCAR_GEN3
	default "u-boot.itb" if !BINMAN && SPL_LOAD_FIT && (ARCH_ROCKCHIP || \
				ARCH_SUNXI || RISCV || ARCH_ZYNQMP)
	default "u-boot.kwb" if ARCH_KIRKWOOD
	default "u-boot-with-spl.bin" if ARCH_AT91 && SPL_NAND_SUPPORT
	default "u-boot-with-spl.imx" if ARCH_MX6 && SPL
	help
	  Some SoCs need special image types (e.g. U-Boot binary
	  with a special header) as build targets. By defining
	  CONFIG_BUILD_TARGET in the SoC / board header, this
	  special image will be automatically built upon calling
	  make / buildman.

config HAS_BOARD_SIZE_LIMIT
	bool "Define a maximum size for the U-Boot image"
	default y if RCAR_GEN3
	help
	  In some cases, we need to enforce a hard limit on how big the U-Boot
	  image itself can be.

config BOARD_SIZE_LIMIT
	int "Maximum size of the U-Boot image in bytes"
	default 1048576 if RCAR_GEN3
	depends on HAS_BOARD_SIZE_LIMIT
	help
	  Maximum size of the U-Boot image. When defined, the build system
	  checks that the actual size does not exceed it.  This does not
	  include SPL nor TPL, on platforms that use that functionality, they
	  have a separate option to restict size.

config SYS_CUSTOM_LDSCRIPT
	bool "Use a custom location for the U-Boot linker script"
	help
	  Normally when linking U-Boot we will look in the board directory,
	  the CPU directory and finally the "cpu" directory of the architecture
	  for the ile "u-boot.lds" and use that as our linker.  However, in
	  some cases we need to provide a different linker script.  To do so,
	  enable this option and then provide the location under
	  CONFIG_SYS_LDSCRIPT.

config SYS_LDSCRIPT
	depends on SYS_CUSTOM_LDSCRIPT
	string "Custom ldscript location"
	help
	  Path within the source tree to the linker script to use for the
	  main U-Boot binary.

config SYS_LOAD_ADDR
	hex "Address in memory to use by default"
	default 0x01000000 if ARCH_SOCFPGA
	default 0x02000000 if PPC || X86
	default 0x81000000 if MACH_SUNIV
	default 0x22000000 if MACH_SUN9I
	default 0x42000000 if ARCH_SUNXI
	default 0x82000000 if ARCH_KEYSTONE || ARCH_OMAP2PLUS || ARCH_K3
	default 0x82000000 if ARCH_MX6 && (MX6SL || MX6SLL  || MX6SX || MX6UL || MX6ULL)
	default 0x12000000 if ARCH_MX6 && !(MX6SL || MX6SLL  || MX6SX || MX6UL || MX6ULL)
	default 0x80800000 if ARCH_MX7
	default 0x90000000 if FSL_LSCH2 || FSL_LSCH3
	help
	  Address in memory to use as the default safe load address.

config ERR_PTR_OFFSET
	hex
	default 0x0
	help
	  Some U-Boot pointers have redundant information, so we can use a
	  scheme where we can return either an error code or a pointer with the
	  same return value. The default implementation just casts the pointer
	  to a number, however, this may fail on platforms where the end of the
	  address range is used for valid pointers (e.g. 0xffffff00 is a valid
	  heap pointer in socfpga SPL).
	  For such platforms, this value provides an upper range of those error
	  pointer values - up to 'MAX_ERRNO' bytes below this value must be
	  unused/invalid addresses.

config PLATFORM_ELFENTRY
	string
	default "__start" if MIPS
	default "_start"

config STACK_SIZE
	hex "Define max stack size that can be used by U-Boot"
	default 0x4000000 if ARCH_VERSAL || ARCH_ZYNQMP
	default 0x200000 if MICROBLAZE
	default 0x1000000
	help
	  Define Max stack size that can be used by U-Boot. This value is used
	  by the UEFI sub-system. On some boards initrd_high is calculated as
	  base stack pointer minus this stack size.

config SYS_MEM_TOP_HIDE
	hex "Exclude some memory from U-Boot / OS information"
	default 0x0
	help
	  If set, this specified memory area will get subtracted from the top
	  (end) of RAM and won't get "touched" at all by U-Boot. By fixing up
	  gd->ram_size the OS / next stage should gets passed the now
	  "corrected" memory size and won't touch it either.
	  WARNING: Please make sure that this value is a multiple of the OS
	  page size.

config SYS_HAS_SRAM
	bool
	default y if TARGET_PIC32MZDASK
	default y if TARGET_DEVKIT8000
	default y if TARGET_TRICORDER
	help
	  Enable this to allow support for the on board SRAM.
	  SRAM base address is controlled by CONFIG_SYS_SRAM_BASE.
	  SRAM size is controlled by CONFIG_SYS_SRAM_SIZE.

config SYS_SRAM_BASE
	hex
	default 0x80000000 if TARGET_PIC32MZDASK
	default 0x40200000 if TARGET_DEVKIT8000
	default 0x40200000 if TARGET_TRICORDER
	default 0x0

config SYS_SRAM_SIZE
	hex
	default 0x00080000 if TARGET_PIC32MZDASK
	default 0x10000 if TARGET_DEVKIT8000
	default 0x10000 if TARGET_TRICORDER
	default 0x0

config MP
	bool "Support for multiprocessor"
	help
	  This provides an option to bringup different processors
	  in multiprocessor cases.

config EXAMPLES
	bool "Compile API examples"
	depends on !SANDBOX
	default y if ARCH_QEMU
	help
	  U-Boot provides an API for standalone applications. Examples are
	  provided in directory examples/.

endmenu		# General setup

source "api/Kconfig"

source "boot/Kconfig"

source "common/Kconfig"

source "cmd/Kconfig"

source "disk/Kconfig"

source "dts/Kconfig"

source "env/Kconfig"

source "net/Kconfig"

source "drivers/Kconfig"

source "fs/Kconfig"

source "lib/Kconfig"

source "test/Kconfig"

source "tools/Kconfig"
