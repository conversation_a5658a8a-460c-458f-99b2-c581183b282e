# Configuration file for fw_(printenv/setenv) utility.
# Up to two entries are valid, in this case the redundant
# environment sector is assumed present.
# Notice, that the "Number of sectors" is not required on NOR and SPI-dataflash.
# Futhermore, if the Flash sector size is omitted, this value is assumed to
# be the same as the Environment size, which is valid for NOR and SPI-dataflash
# Device offset must be prefixed with 0x to be parsed as a hexadecimal value.

# NOR example
# MTD device name	Device offset	Env. size	Flash sector size	Number of sectors
/dev/mtd1		0x0000		0x4000		0x4000
/dev/mtd2		0x0000		0x4000		0x4000

# MTD SPI-dataflash example
# MTD device name	Device offset	Env. size	Flash sector size	Number of sectors
#/dev/mtd5		0x4200		0x4200
#/dev/mtd6		0x4200		0x4200

# NAND example
#/dev/mtd0		0x4000		0x4000		0x20000			2

# On a block device a negative offset is treated as a backwards offset from the
# end of the device/partition, rather than a forwards offset from the start.

# Block device example
#/dev/mmcblk0		0xc0000		0x20000
#/dev/mmcblk0		-0x20000	0x20000

# VFAT example
#/boot/uboot.env	0x0000          0x4000

# UBI volume
#/dev/ubi0_0		0x0		0x1f000		0x1f000
#/dev/ubi0_1		0x0		0x1f000		0x1f000

# UBI volume by name
#/dev/ubi0:env		0x0		0x1f000		0x1f000
#/dev/ubi0:env-redund	0x0		0x1f000		0x1f000
