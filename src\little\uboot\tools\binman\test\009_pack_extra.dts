/dts-v1/;

/ {
	#address-cells = <1>;
	#size-cells = <1>;

	binman {
		u-boot {
			pad-before = <3>;
			pad-after = <5>;
		};

		u-boot-align-size-nop {
			type = "u-boot";
			align-size = <4>;
		};

		u-boot-align-size {
			type = "u-boot";
			align = <16>;
			align-size = <32>;
		};

		u-boot-align-end {
			type = "u-boot";
			align-end = <64>;
		};

		u-boot-align-both {
			type = "u-boot";
			align = <64>;
			align-end = <128>;
		};
	};
};
