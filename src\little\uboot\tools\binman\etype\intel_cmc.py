# SPDX-License-Identifier: GPL-2.0+
# Copyright (c) 2016 Google, Inc
# Written by <PERSON> <<EMAIL>>
#
# Entry-type module for Intel Chip Microcode binary blob
#

from binman.etype.blob_ext import Entry_blob_ext

class Entry_intel_cmc(Entry_blob_ext):
    """Intel Chipset Micro Code (CMC) file

    Properties / Entry arguments:
        - filename: Filename of file to read into entry

    This file contains microcode for some devices in a special format. An
    example filename is 'Microcode/C0_22211.BIN'.

    See README.x86 for information about x86 binary blobs.
    """
    def __init__(self, section, etype, node):
        super().__init__(section, etype, node)
