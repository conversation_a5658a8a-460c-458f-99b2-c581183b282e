// SPDX-License-Identifier: GPL-2.0+

/dts-v1/;

/ {
	#address-cells = <1>;
	#size-cells = <1>;

	binman {
		u-boot {
		};
		fit {
			description = "test-desc";
			#address-cells = <1>;
			fit,fdt-list = "of-list";

			images {
				@fdt-SEQ {
					description = "fdt-NAME.dtb";
					type = "flat_dt";
					compression = "none";
				};
				atf: @atf-SEQ {
					fit,operation = "split-elf";
					description = "ARM Trusted Firmware";
					type = "firmware";
					arch = "arm64";
					os = "arm-trusted-firmware";
					compression = "none";
					fit,load;
					fit,entry;
					fit,data;

					atf-bl31 {
					};
				};

				@tee-SEQ {
					fit,operation = "split-elf";
					description = "TEE";
					type = "tee";
					arch = "arm64";
					os = "tee";
					compression = "none";
					fit,load;
					fit,entry;
					fit,data;

					tee-os {
					};
				};
			};

			configurations {
				default = "@config-DEFAULT-SEQ";
				config: @config-SEQ {
					description = "conf-NAME.dtb";
					fdt = "fdt-SEQ";
					fit,loadables;
				};
			};
		};

		u-boot-nodtb {
		};
	};
};
