commit b9da5f937bd5ea4931ea17459bf79b2905d9594d
Author: <PERSON> <<EMAIL>>
Date:   Sat Apr 15 15:39:08 2017 -0600

    pci: Correct cast for sandbox

    This gives a warning with some native compilers:

    cmd/pci.c:152:11: warning: format ‘%llx’ expects argument of type
       ‘long long unsigned int’, but argument 3 has type
       ‘u64 {aka long unsigned int}’ [-Wformat=]

    Fix it with a cast.

    Signed-off-by: <PERSON> <<EMAIL>>
    Commit-changes: 2
    - second revision change

    Series-notes:
    some notes
    about some things
    from the first commit
    END

    Commit-notes:
    Some notes about
    the first commit
    END

commit 5ab48490f03051875ab13d288a4bf32b507d76fd
Author: <PERSON> <<EMAIL>>
Date:   Sat Apr 15 15:39:08 2017 -0600

    fdt: Correct cast for sandbox in fdtdec_setup_mem_size_base()

    This gives a warning with some native compilers:

    lib/fdtdec.c:1203:8: warning: format ‘%llx’ expects argument of type
       ‘long long unsigned int’, but argument 3 has type
       ‘long unsigned int’ [-Wformat=]

    Fix it with a cast.

    Signed-off-by: <PERSON> <<EMAIL>>
    Series-to: u-boot
    Series-prefix: RFC
    Series-postfix: some-branch
    Series-cc: Stefan Brüns <<EMAIL>>
    Cover-letter-cc: Lord Mëlchett <<EMAIL>>
    Series-version: 3
    Patch-cc: fred
    Series-process-log: sort, uniq
    Series-changes: 4
    - Some changes
    - Multi
      line
      change

    Commit-changes: 2
    - Changes only for this commit

    Cover-changes: 4
    - Some notes for the cover letter

    Cover-letter:
    test: A test patch series
    This is a test of how the cover
    letter
    works
    END
