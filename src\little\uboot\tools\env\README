
This is a demo implementation of a Linux command line tool to access
the U-Boot's environment variables.

In order to cross-compile fw_printenv, run
    make CROSS_COMPILE=<your cross-compiler prefix> envtools
in the root directory of the U-Boot distribution. For example,
    make CROSS_COMPILE=arm-linux- envtools

You should then create a symlink from fw_setenv to fw_printenv. They use
the same program and its function depends on its basename.

For the run-time utility configuration uncomment the line
#define CONFIG_FILE  "/etc/fw_env.config"
in fw_env.h.

For building against older versions of the MTD headers (meaning before
v2.6.8-rc1) it is required to pass the argument "MTD_VERSION=old" to
make.

See comments in the fw_env.config file for definitions for the
particular board.

Configuration can also be done via #defines in the fw_env.h file. The
following lines are relevant:

#define HAVE_REDUND	/* For systems with 2 env sectors */
#define DEVICE1_NAME	"/dev/mtd1"
#define DEVICE2_NAME	"/dev/mtd2"
#define DEVICE1_OFFSET    0x0000
#define ENV1_SIZE         0x4000
#define DEVICE1_ESIZE     0x4000
#define DEVICE1_ENVSECTORS     2
#define DEVICE2_OFFSET    0x0000
#define ENV2_SIZE         0x4000
#define DEVICE2_ESIZE     0x4000
#define DEVICE2_ENVSECTORS     2

Un-define HAVE_REDUND, if you want to use the utilities on a system
that does not have support for redundant environment enabled.
If HAVE_REDUND is undefined, DEVICE2_NAME is ignored,
as is ENV2_SIZE and DEVICE2_ESIZE.

The DEVICEx_NAME constants define which MTD character devices are to
be used to access the environment.

The DEVICEx_OFFSET constants define the environment offset within the
MTD character device.

ENVx_SIZE defines the size in bytes taken by the environment, which
may be less then flash sector size, if the environment takes less
then 1 sector.

DEVICEx_ESIZE defines the size of the first sector in the flash
partition where the environment resides.

DEVICEx_ENVSECTORS defines the number of sectors that may be used for
this environment instance. On NAND this is used to limit the range
within which bad blocks are skipped, on NOR it is not used.

To prevent losing changes to the environment and to prevent confusing the MTD
drivers, a lock file at /var/lock/fw_printenv.lock is used to serialize access
to the environment.
