// SPDX-License-Identifier: GPL-2.0+

/dts-v1/;

/ {
	#address-cells = <1>;
	#size-cells = <1>;

	binman {
		u-boot {
		};
		fit {
			fit,external-offset = <0x400>;
			description = "test-desc";
			#address-cells = <1>;

			images {
				kernel {
					description = "Vanilla Linux kernel";
					type = "kernel";
					arch = "ppc";
					os = "linux";
					compression = "gzip";
					load = <00000000>;
					entry = <00000000>;
					hash-1 {
						algo = "crc32";
					};
					hash-2 {
						algo = "sha1";
					};
					u-boot {
					};
				};
				fdt-1 {
					description = "Flattened Device Tree blob";
					type = "flat_dt";
					arch = "ppc";
					compression = "none";
					hash-1 {
						algo = "crc32";
					};
					hash-2 {
						algo = "sha1";
					};
					_testing {
						return-contents-later;
					};
				};
			};

			configurations {
				default = "conf-1";
				conf-1 {
					description = "Boot Linux kernel with FDT blob";
					kernel = "kernel";
					fdt = "fdt-1";
				};
			};
		};
		u-boot-nodtb {
		};
	};
};
