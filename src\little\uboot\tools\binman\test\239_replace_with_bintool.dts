// SPDX-License-Identifier: GPL-2.0+

/dts-v1/;

/ {
	#address-cells = <1>;
	#size-cells = <1>;

	binman {
		size = <0xc00>;
		allow-repack;

		u-boot {
		};

		_testing {
			require-bintool-for-contents;
			require-bintool-for-pack;
		};

		fdtmap {
		};

		u-boot2 {
			type = "u-boot";
		};

		text {
			text = "some text";
		};

		u-boot-dtb {
		};

		image-header {
			location = "end";
		};
	};
};
