# SPDX-License-Identifier: GPL-2.0+
# Copyright (c) 2016 Google, Inc
# Written by <PERSON> <<EMAIL>>
#
# Entry-type module for external blobs, not built by U-Boot
#

import os

from binman.etype.blob import Entry_blob
from dtoc import fdt_util
from patman import tools
from patman import tout

class Entry_blob_ext(Entry_blob):
    """Externally built binary blob

    Note: This should not be used by itself. It is normally used as a parent
    class by other entry types.

    If the file providing this blob is missing, binman can optionally ignore it
    and produce a broken image with a warning.

    See 'blob' for Properties / Entry arguments.
    """
    def __init__(self, section, etype, node):
        Entry_blob.__init__(self, section, etype, node)
        self.external = True

    def SetAllowFakeBlob(self, allow_fake):
        """Set whether the entry allows to create a fake blob

        Args:
            allow_fake_blob: True if allowed, False if not allowed
        """
        self.allow_fake = allow_fake
