#include "compiler.h"

enum {
	MODE_GEN_INFO,
	MODE_GEN_DATA,
	MODE_GEN_BMP
};

typedef struct bitmap_s {		/* bitmap description */
	uint16_t width;
	uint16_t height;
	uint8_t	palette[256*3];
	uint8_t	*data;
} bitmap_t;

#define DEFAULT_CMAP_SIZE	16	/* size of default color map	*/

void usage(const char *prog)
{
	fprintf(stderr, "Usage: %s [--gen-info|--gen-data|--gen-bmp] file\n",
		prog);
}

/*
 * Neutralize little endians.
 */
uint16_t le_short(uint16_t x)
{
    uint16_t val;
    uint8_t *p = (uint8_t *)(&x);

    val =  (*p++ & 0xff) << 0;
    val |= (*p & 0xff) << 8;

    return val;
}

void skip_bytes (FILE *fp, int n)
{
	while (n-- > 0)
		fgetc (fp);
}

__attribute__ ((__noreturn__))
int error (char * msg, FILE *fp)
{
	fprintf (stderr, "ERROR: %s\n", msg);

	fclose (fp);

	exit (EXIT_FAILURE);
}

void gen_info(bitmap_t *b, uint16_t n_colors)
{
	printf("/*\n"
		" * Automatically generated by \"tools/bmp_logo\"\n"
		" *\n"
		" * DO NOT EDIT\n"
		" *\n"
		" */\n\n\n"
		"#ifndef __BMP_LOGO_H__\n"
		"#define __BMP_LOGO_H__\n\n"
		"#define BMP_LOGO_WIDTH\t\t%d\n"
		"#define BMP_LOGO_HEIGHT\t\t%d\n"
		"#define BMP_LOGO_COLORS\t\t%d\n"
		"#define BMP_LOGO_OFFSET\t\t%d\n\n"
		"extern unsigned short bmp_logo_palette[];\n"
		"extern unsigned char bmp_logo_bitmap[];\n\n"
		"#endif /* __BMP_LOGO_H__ */\n",
		b->width, b->height, n_colors,
		DEFAULT_CMAP_SIZE);
}

int main (int argc, char *argv[])
{
	int	mode, i, x;
	int	size;
	FILE	*fp;
	bitmap_t bmp;
	bitmap_t *b = &bmp;
	uint16_t data_offset, n_colors, hdr_size;

	if (argc < 3) {
		usage(argv[0]);
		exit (EXIT_FAILURE);
	}

	if (!strcmp(argv[1], "--gen-info"))
		mode = MODE_GEN_INFO;
	else if (!strcmp(argv[1], "--gen-data"))
		mode = MODE_GEN_DATA;
	else if (!strcmp(argv[1], "--gen-bmp"))
		mode = MODE_GEN_BMP;
	else {
		usage(argv[0]);
		exit(EXIT_FAILURE);
	}

	fp = fopen(argv[2], "rb");
	if (!fp) {
		perror(argv[2]);
		exit (EXIT_FAILURE);
	}

	if (fgetc (fp) != 'B' || fgetc (fp) != 'M')
		error ("Input file is not a bitmap", fp);

	/*
	 * read width and height of the image, and the number of colors used;
	 * ignore the rest
	 */
	skip_bytes (fp, 8);
	if (fread (&data_offset, sizeof (uint16_t), 1, fp) != 1)
		error ("Couldn't read bitmap data offset", fp);
	skip_bytes(fp, 2);
	if (fread(&hdr_size,   sizeof(uint16_t), 1, fp) != 1)
		error("Couldn't read bitmap header size", fp);
	if (hdr_size < 40)
		error("Invalid bitmap header", fp);
	skip_bytes(fp, 2);
	if (fread (&b->width,   sizeof (uint16_t), 1, fp) != 1)
		error ("Couldn't read bitmap width", fp);
	skip_bytes (fp, 2);
	if (fread (&b->height,  sizeof (uint16_t), 1, fp) != 1)
		error ("Couldn't read bitmap height", fp);
	skip_bytes (fp, 22);
	if (fread (&n_colors, sizeof (uint16_t), 1, fp) != 1)
		error ("Couldn't read bitmap colors", fp);
	skip_bytes(fp, hdr_size - 34);

	/*
	 * Repair endianess.
	 */
	data_offset = le_short(data_offset);
	b->width = le_short(b->width);
	b->height = le_short(b->height);
	n_colors = le_short(n_colors);
	size = b->width * b->height;

	/* assume we are working with an 8-bit file */
	if ((n_colors == 0) || (n_colors > 256 - DEFAULT_CMAP_SIZE)) {
		/* reserve DEFAULT_CMAP_SIZE color map entries for default map */
		n_colors = 256 - DEFAULT_CMAP_SIZE;
	}

	if (mode == MODE_GEN_INFO) {
		gen_info(b, n_colors);
		goto out;
	}

	printf("/*\n"
		" * Automatically generated by \"tools/bmp_logo\"\n"
		" *\n"
		" * DO NOT EDIT\n"
		" *\n"
		" */\n\n\n"
		"#ifndef __BMP_LOGO_DATA_H__\n"
		"#define __BMP_LOGO_DATA_H__\n\n");

	/* read and print the palette information */
	printf("unsigned short bmp_logo_palette[] = {\n");

	for (i=0; i<n_colors; ++i) {
		b->palette[(int)(i*3+2)] = fgetc(fp);
		b->palette[(int)(i*3+1)] = fgetc(fp);
		b->palette[(int)(i*3+0)] = fgetc(fp);
		x=fgetc(fp);

		printf ("%s0x0%X%X%X,%s",
			((i%8) == 0) ? "\t" : "  ",
			(b->palette[(int)(i*3+0)] >> 4) & 0x0F,
			(b->palette[(int)(i*3+1)] >> 4) & 0x0F,
			(b->palette[(int)(i*3+2)] >> 4) & 0x0F,
			((i%8) == 7) ? "\n" : ""
		);
	}

	/* seek to offset indicated by file header */
	if (mode == MODE_GEN_BMP) {
		/* copy full bmp file */
		fseek(fp, 0L, SEEK_END);
		size = ftell(fp);
		fseek(fp, 0L, SEEK_SET);
	} else {
		fseek(fp, (long)data_offset, SEEK_SET);
	}

	/* allocate memory */
	b->data = (uint8_t *)malloc(size);
	if (!b->data)
		error("Error allocating memory for file", fp);

	/* read the bitmap; leave room for default color map */
	printf ("\n");
	printf ("};\n");
	printf ("\n");
	printf("unsigned char bmp_logo_bitmap[] = {\n");
	if (mode == MODE_GEN_BMP) {
		/* write full bmp */
		for (i = 0; i < size; i++)
			b->data[i] = (uint8_t)fgetc(fp);
	} else {
		for (i = (b->height - 1) * b->width; i >= 0; i -= b->width) {
			for (x = 0; x < b->width; x++) {
				b->data[i + x] = (uint8_t)fgetc(fp)
						+ DEFAULT_CMAP_SIZE;
			}
		}
	}

	for (i = 0; i < size; ++i) {
		if ((i%8) == 0)
			putchar ('\t');
		printf ("0x%02X,%c",
			b->data[i],
			((i%8) == 7) ? '\n' : ' '
		);
	}
	printf ("\n"
		"};\n\n"
		"#endif /* __BMP_LOGO_DATA_H__ */\n"
	);

out:
	fclose(fp);
	return 0;
}
