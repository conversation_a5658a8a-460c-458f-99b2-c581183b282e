// SPDX-License-Identifier: GPL-2.0+
/dts-v1/;

/ {
	#address-cells = <1>;
	#size-cells = <1>;

	binman {
		u_boot: u-boot {
		};

		vblock {
			content = <&u_boot &dtb>;
			keyblock = "firmware.keyblock";
			signprivate = "firmware_data_key.vbprivk";
			version = <1>;
			kernelkey = "kernel_subkey.vbpubk";
			preamble-flags = <1>;
		};

		/*
		 * Put this after the vblock so that its contents are not
		 * available when the vblock first tries to obtain its contents
		 */
		dtb: u-boot-dtb {
		};
	};
};
