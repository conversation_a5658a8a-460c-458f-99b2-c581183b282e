// SPDX-License-Identifier: GPL-2.0+

/dts-v1/;

/ {
	#address-cells = <1>;
	#size-cells = <1>;

	binman {
		fit {
			description = "test-desc";
			#address-cells = <1>;

			images {
				test {
					description = "Something using a bintool";
					type = "kernel";
					arch = "arm";
					os = "linux";
					compression = "gzip";
					load = <00000000>;
					entry = <00000000>;

					gbb {
						size = <0x2180>;
					};
				};
			};

			configurations {
				default = "conf-1";
				conf-1 {
					description = "Boot bintool output";
					kernel = "kernel";
				};
			};
		};
	};
};
