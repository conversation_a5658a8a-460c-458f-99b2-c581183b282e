// SPDX-License-Identifier: GPL-2.0+

/dts-v1/;

/ {
	#address-cells = <1>;
	#size-cells = <1>;

	binman {
		u-boot {
		};
		fit {
			description = "test-desc";
			#address-cells = <1>;
			fit,fdt-list = "of-list";

			images {
				kernel {
					description = "Vanilla Linux kernel";
					type = "kernel";
					arch = "ppc";
					os = "linux";
					compression = "gzip";
					load = <00000000>;
					entry = <00000000>;
					hash-1 {
						algo = "crc32";
					};
					hash-2 {
						algo = "sha1";
					};
					u-boot {
					};
				};
				@fdt-SEQ {
					description = "fdt-NAME.dtb";
					type = "flat_dt";
					compression = "none";
					hash {
						algo = "sha256";
					};
				};
			};

			configurations {
				default = "@config-DEFAULT-SEQ";
				@config-SEQ {
					description = "conf-NAME.dtb";
					firmware = "uboot";
					loadables = "atf";
					fdt = "fdt-SEQ";
				};
			};
		};
		u-boot-nodtb {
		};
	};
};
