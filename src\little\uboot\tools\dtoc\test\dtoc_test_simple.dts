// SPDX-License-Identifier: GPL-2.0+
/*
 * Test device tree file for dtoc
 *
 * Copyright 2017 Google, Inc
 */

 /dts-v1/;

/ {
	#address-cells = <1>;
	#size-cells = <1>;
	spl-test {
		u-boot,dm-pre-reloc;
		compatible = "sandbox,spl-test";
		boolval;
		maybe-empty-int = <>;
		intval = <1>;
		int64val = /bits/ 64 <0x123456789abcdef0>;
		intarray = <2 3 4>;
		byteval = [05];
		bytearray = [06];
		longbytearray = [09 0a 0b 0c 0d 0e 0f 10 11];
		stringval = "message";
		stringarray = "multi-word", "message";
		notstring = [20 21 22 10 00];
	};

	spl-test2 {
		u-boot,dm-pre-reloc;
		compatible = "sandbox,spl-test";
		intval = <3>;
		intarray = <5>;
		byteval = [08];
		bytearray = [01 23 34];
		longbytearray = [09 0a 0b 0c];
		stringval = "message2";
		stringarray = "another", "multi-word", "message";
		acpi-name =  "\\_SB.GPO0";
	};

	spl-test3 {
		u-boot,dm-pre-reloc;
		compatible = "sandbox,spl-test";
		stringarray = "one";
		longbytearray = [09 0a 0b 0c 0d 0e 0f 10];
		maybe-empty-int = <1>;
	};

	i2c@0 {
		compatible = "sandbox,i2c";
		u-boot,dm-pre-reloc;
		#address-cells = <1>;
		#size-cells = <0>;
		pmic@9 {
			compatible = "sandbox,pmic";
			u-boot,dm-pre-reloc;
			reg = <9>;
			low-power;
		};
	};

	orig-node {
		orig = <1 23 4>;
		args = "-n first", "second", "-p", "123,456", "-x";
		args2 = "a space", "there";
		args3 = "-n first second -p 123,456 -x";
	};
};
